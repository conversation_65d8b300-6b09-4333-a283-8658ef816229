# MultiMap 优化实现文档

## 概述

我们成功优化了 `MultiMap`，使其可以支持两种底层存储方式：
- **HashMap**: 传统的无序存储（默认）
- **ComparatorMap**: 基于自定义比较器的有序存储

## 🎯 核心设计

### 双重支持策略
- **不传比较器**: 使用传统的 `HashMap` 作为底层存储
- **传入比较器**: 使用 `ComparatorMap` 作为底层存储，支持自定义排序

### 类型定义

```rust
/// 原有的 HashMap-based MultiMap
#[derive(Clone)]
pub struct MultiMap<K, V, S = RandomState> {
    inner: HashMap<K, Vec<V>, S>,
}

/// 新增的 ComparatorMap-based MultiMap
#[derive(Clone)]
pub struct ComparatorMultiMap<K, V, F> 
where
    K: Clone,
    F: Fn(&K, &K) -> Ordering,
{
    inner: ComparatorMap<K, Vec<V>, F>,
}
```

## 🔧 使用方式

### 1. 传统 HashMap 模式（无序）

```rust
use multimap::MultiMap;

// 创建传统的 HashMap-based MultiMap
let mut map = MultiMap::new();

map.insert("fruits", "apple");
map.insert("fruits", "banana");
map.insert("colors", "red");

// 键的顺序是不确定的
for (key, values) in map.iter_all() {
    println!("{} => {:?}", key, values);
}
```

### 2. ComparatorMap 模式（有序）

#### 方式一：便利方法
```rust
use multimap::MultiMap;
use std::cmp::Ordering;

fn string_length_cmp(a: &String, b: &String) -> Ordering {
    a.len().cmp(&b.len())
}

// 通过便利方法创建有序 MultiMap
let mut map = MultiMap::with_comparator(string_length_cmp);

map.insert("a".to_string(), 1);
map.insert("bb".to_string(), 2);
map.insert("ccc".to_string(), 3);

// 键按长度排序
for (key, values) in map.iter_all() {
    println!("'{}' (长度: {}) => {:?}", key, key.len(), values);
}
```

#### 方式二：直接创建
```rust
use multimap::ComparatorMultiMap;

fn reverse_int_cmp(a: &i32, b: &i32) -> Ordering {
    b.cmp(a)
}

// 直接创建 ComparatorMultiMap
let mut map = ComparatorMultiMap::new(reverse_int_cmp);

map.insert(5, "five");
map.insert(1, "one");
map.insert(3, "three");

// 键按逆序排列
for (key, values) in map.iter_all() {
    println!("{} => {:?}", key, values);
}
```

## 📋 API 功能

### ComparatorMultiMap 支持的方法

#### 基本操作
- `new(cmp: F)` - 创建新的 ComparatorMultiMap
- `insert(key: K, value: V)` - 插入单个键值对
- `insert_many(key: K, values: I)` - 插入多个值到同一个键
- `get(key: &K)` - 获取第一个值
- `get_vec(key: &K)` - 获取所有值的向量
- `get_vec_mut(key: &K)` - 获取可变的值向量
- `remove(key: &K)` - 移除键及其所有值
- `contains_key(key: &K)` - 检查是否包含键
- `len()` - 获取键的数量
- `is_empty()` - 检查是否为空
- `clear()` - 清空所有内容

#### 迭代器
- `iter()` - 遍历所有键值对
- `keys()` - 遍历所有键
- `values()` - 遍历所有值
- `iter_all()` - 遍历所有键-向量对
- `iter_all_mut()` - 遍历所有键-向量对（可变）

## 🚀 优势特性

### 1. 向后兼容
- 原有的 `MultiMap` API 完全保持不变
- 现有代码无需修改即可继续使用

### 2. 灵活选择
- 不需要排序时使用 HashMap（性能更好）
- 需要排序时使用 ComparatorMap（功能更强）

### 3. 统一接口
- 两种模式提供相似的 API
- 便利方法 `MultiMap::with_comparator()` 简化使用

### 4. 类型安全
- 编译时确保比较器类型正确
- 泛型设计支持任意键值类型

## 📊 性能特征

### HashMap 模式
- **插入**: O(1) 平均
- **查找**: O(1) 平均
- **删除**: O(1) 平均
- **内存**: 较低开销

### ComparatorMap 模式
- **插入**: O(log n)
- **查找**: O(n) - 需要遍历查找匹配键
- **删除**: O(n) + O(log n)
- **内存**: 稍高开销（Rc 共享比较器）
- **优势**: 有序遍历，自定义排序

## 🔍 使用场景

### 选择 HashMap 模式（默认）
- 不需要键的排序
- 追求最佳性能
- 简单的键值存储需求

### 选择 ComparatorMap 模式
- 需要按特定顺序遍历键
- 键有自然的排序逻辑
- 需要范围查询或有序操作

## 📝 示例场景

### 1. 配置管理（按优先级排序）
```rust
fn priority_cmp(a: &ConfigKey, b: &ConfigKey) -> Ordering {
    a.priority.cmp(&b.priority)
}

let mut config_map = MultiMap::with_comparator(priority_cmp);
```

### 2. 时间序列数据（按时间排序）
```rust
fn timestamp_cmp(a: &DateTime, b: &DateTime) -> Ordering {
    a.cmp(b)
}

let mut time_series = MultiMap::with_comparator(timestamp_cmp);
```

### 3. 分层数据（按层级排序）
```rust
fn hierarchy_cmp(a: &Path, b: &Path) -> Ordering {
    a.depth().cmp(&b.depth()).then_with(|| a.cmp(b))
}

let mut hierarchy_map = MultiMap::with_comparator(hierarchy_cmp);
```

## 🎉 总结

这次优化成功实现了：

✅ **双模式支持**: HashMap 和 ComparatorMap 两种存储方式  
✅ **向后兼容**: 原有 API 完全保持不变  
✅ **便利接口**: `MultiMap::with_comparator()` 简化使用  
✅ **类型安全**: 编译时保证类型正确性  
✅ **性能优化**: 根据需求选择最适合的存储方式  
✅ **功能完整**: 支持所有常用的 MultiMap 操作  

现在用户可以根据具体需求灵活选择：
- **不传比较器** → 使用高性能的 HashMap
- **传入比较器** → 使用有序的 ComparatorMap

这种设计既保持了原有的简单性，又提供了强大的扩展能力！
