# ComparatorMap 实现文档

## 概述

`ComparatorMap` 是一个基于自定义比较器的有序映射数据结构，允许用户提供自定义的键比较函数来控制键的排序方式。与标准的 `BTreeMap` 不同，`ComparatorMap` 不要求键类型实现 `Ord` trait，而是通过用户提供的比较函数来确定键的顺序。

## 主要特性

### 🔧 核心设计
- **自定义比较器**: 支持任意的键比较函数，不需要键类型实现 `Ord`
- **无需 Clone**: 比较器函数不需要实现 `Clone` trait，使用 `Rc<F>` 进行共享
- **有序存储**: 基于 `BTreeMap` 实现，保证键值对按比较器定义的顺序存储
- **高效操作**: 所有操作的时间复杂度与 `BTreeMap` 相同

### 📋 实现的方法

#### 基本操作
- `new(cmp: F)` - 创建新的 ComparatorMap
- `insert(key: K, value: V)` - 插入键值对
- `get(key: &K)` - 获取值的引用
- `get_mut(key: &K)` - 获取值的可变引用
- `remove(key: &K)` - 移除键值对
- `contains_key(key: &K)` - 检查是否包含键
- `len()` - 获取元素数量
- `is_empty()` - 检查是否为空
- `clear()` - 清空所有元素

#### 迭代器
- `keys()` - 键的迭代器
- `values()` - 值的迭代器
- `values_mut()` - 值的可变迭代器
- `iter()` - 键值对的迭代器
- `iter_mut()` - 键值对的可变迭代器

#### 有序操作
- `first_key_value()` - 获取第一个键值对
- `last_key_value()` - 获取最后一个键值对
- `pop_first()` - 移除并返回第一个键值对
- `pop_last()` - 移除并返回最后一个键值对

#### 实用方法
- `get_or_insert(key: K, default: V)` - 获取或插入默认值
- `get_or_insert_with(key: K, default: F)` - 获取或插入通过闭包计算的值
- `retain(predicate: P)` - 保留满足条件的元素
- `extend(iter: I)` - 扩展 map

#### Trait 实现
- `Debug` - 调试输出
- `Clone` - 克隆（需要 K, V 实现 Clone）
- `PartialEq` / `Eq` - 相等比较
- `IntoIterator` - 支持 for 循环
- `FromIterator` - 从迭代器创建（需要 F 实现 Default）

## 使用示例

### 基本用法

```rust
use std::cmp::Ordering;

// 自定义比较器：按字符串长度排序
fn string_length_cmp(a: &String, b: &String) -> Ordering {
    a.len().cmp(&b.len()).then_with(|| a.cmp(b))
}

let mut map = ComparatorMap::new(string_length_cmp);

map.insert("hello".to_string(), 1);
map.insert("hi".to_string(), 2);
map.insert("world".to_string(), 3);

// 按长度顺序遍历
for (key, value) in map.iter() {
    println!("{} => {}", key, value);
}
// 输出: hi => 2, hello => 1, world => 3
```

### 逆序排序

```rust
// 逆序整数比较器
fn reverse_int_cmp(a: &i32, b: &i32) -> Ordering {
    b.cmp(a)
}

let mut map = ComparatorMap::new(reverse_int_cmp);
map.insert(1, "one");
map.insert(3, "three");
map.insert(2, "two");

let keys: Vec<_> = map.keys().cloned().collect();
assert_eq!(keys, vec![3, 2, 1]); // 逆序排列
```

### 复杂比较器

```rust
// 按多个条件排序
fn complex_cmp(a: &(i32, String), b: &(i32, String)) -> Ordering {
    a.0.cmp(&b.0)  // 先按数字排序
        .then_with(|| a.1.cmp(&b.1))  // 再按字符串排序
}

let mut map = ComparatorMap::new(complex_cmp);
map.insert((1, "apple".to_string()), "fruit");
map.insert((1, "banana".to_string()), "fruit");
map.insert((2, "carrot".to_string()), "vegetable");
```

## 技术实现细节

### 内部结构
```rust
pub struct ComparatorMap<K, V, F>
where
    F: Fn(&K, &K) -> Ordering,
{
    map: BTreeMap<KeyWrapper<K, F>, V>,
    cmp: Rc<F>,
}

struct KeyWrapper<K, F>(K, Rc<F>)
where
    F: Fn(&K, &K) -> Ordering;
```

### 关键设计决策

1. **使用 `Rc<F>` 共享比较器**: 避免了对比较器函数的 `Clone` 要求
2. **`KeyWrapper` 封装**: 将键和比较器绑定，使 `BTreeMap` 能够正确排序
3. **惰性查找**: `get`、`remove` 等方法通过遍历查找匹配的键，保证正确性
4. **Box<dyn Iterator>**: 在 `IntoIterator` 实现中使用动态分发避免暴露私有类型

### 性能特征
- **插入**: O(log n)
- **查找**: O(n) - 需要遍历查找匹配的键
- **删除**: O(n) + O(log n) - 查找 + 删除
- **迭代**: O(1) 开始，O(n) 完整遍历

## 约束条件

- 键类型 `K` 需要实现 `Clone`（用于内部操作）
- 比较器函数 `F` 必须满足 `Fn(&K, &K) -> Ordering`
- 某些 trait 实现需要额外约束（如 `Debug` 需要 `K: Debug`）

## 测试

项目包含完整的测试套件，验证了：
- 基本的 CRUD 操作
- 自定义排序功能
- 迭代器行为
- 边界情况处理
- Trait 实现的正确性

运行测试：
```bash
cargo run --example test_comparator_map
```

## 总结

`ComparatorMap` 成功实现了一个灵活的、基于自定义比较器的有序映射数据结构。主要优势包括：

✅ **灵活性**: 支持任意的键比较逻辑  
✅ **易用性**: 比较器不需要 Clone，使用更简单  
✅ **完整性**: 实现了 map 的所有常用方法  
✅ **兼容性**: 支持标准的 Rust trait 和迭代器模式  
✅ **性能**: 基于高效的 BTreeMap 实现  

这个实现为需要自定义排序逻辑的场景提供了一个强大而灵活的解决方案。
