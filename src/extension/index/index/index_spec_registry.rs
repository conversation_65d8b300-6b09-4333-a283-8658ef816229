use crate::config::app_error::{AppError, AppResult};
use crate::extension::index::index::index_specs::{DefaultIndexSpecs, IndexSpecs};
use crate::extension::index::index::query::index_attribute::IndexAttributeFactory;
use crate::extension::index::index::query::index_spec;
use crate::extension::index::index::query::index_spec::{IndexSpec, OrderType};
use crate::extension::index::scheme_manager::Scheme;
use halo_model::ExtensionGVK;
use std::any::Any;
use std::collections::HashMap;
use std::sync::{Arc, RwLock};

pub struct IndexSpecRegistry {
    pub extension_index_specs: RwLock<HashMap<String, Box<dyn Any + Send + Sync>>>,
}

impl IndexSpecRegistry {
    pub fn new() -> Self {
        IndexSpecRegistry {
            extension_index_specs: RwLock::new(HashMap::new()),
        }
    }

    fn use_default_index_spec<T: ExtensionGVK + Send + Sync>(
        index_specs: &mut DefaultIndexSpecs<T>,
    ) -> AppResult<()> {
        let spec = index_spec::primary_key_index_spec::<T>();
        index_specs.add(spec)?;

        let creation_index_spec = IndexSpec::new(
            "metadata.creationTimestamp".to_string(),
            OrderType::ASC,
            false,
            IndexAttributeFactory::simple_attribute("".to_string(), |e: &T| {
                e.get_metadata()
                    .get_creation_timestamp()
                    .map(|v| v.to_string())
                    .unwrap_or("".to_string())
            }),
        );

        index_specs.add(creation_index_spec)?;

        let deletion_index_spec = IndexSpec::new(
            "metadata.deletionTimestamp".to_string(),
            OrderType::ASC,
            false,
            IndexAttributeFactory::simple_attribute("".to_string(), |e: &T| {
                e.get_metadata()
                    .get_deletion_timestamp()
                    .map(|v| v.to_string())
                    .unwrap_or("".to_string())
            }),
        );

        index_specs.add(deletion_index_spec)?;

        index_specs.add(index_spec::label_index_spec::<T>())?;
        Ok(())
    }

    pub(crate) fn index_for<T: ExtensionGVK + Send + Sync>(
        &self,
        scheme: &Scheme,
    ) -> AppResult<()> {
        let key_space = self.get_key_space(scheme);

        let mut index_specs = DefaultIndexSpecs::new();

        Self::use_default_index_spec::<T>(&mut index_specs)?;

        let mut extension_index_specs = self.extension_index_specs.write().map_err(|_| {
            AppError::InternalServerError("Failed to acquire write lock on index_specs".to_string())
        })?;

        extension_index_specs.insert(
            key_space.clone(),
            Box::new(Arc::new(RwLock::new(index_specs))),
        );

        // self.extension_index_specs.get(&key_space).unwrap();

        Ok(())
    }
    pub(crate) fn consumer<F, T>(&self, scheme: &Scheme, f: F) -> AppResult<()>
    where
        F: Fn(Arc<RwLock<DefaultIndexSpecs<T>>>) -> AppResult<()>,
        T: ExtensionGVK,
    {
        let key_space = self.get_key_space(scheme);
        let mut extension_index_specs = self.extension_index_specs.write().map_err(|_| {
            AppError::InternalServerError("Failed to acquire write lock on index_specs".to_string())
        })?;

        if let Some(index_specs) = extension_index_specs.get_mut(&key_space) {
            if let Some(index_specs) =
                index_specs.downcast_ref::<Arc<RwLock<DefaultIndexSpecs<T>>>>()
            {
                f(Arc::clone(index_specs))?
            }
        }
        Ok(())
    }
    pub(crate) fn get_index_specs<T: ExtensionGVK>(
        &self,
        scheme: &Scheme,
    ) -> AppResult<Arc<RwLock<DefaultIndexSpecs<T>>>> {
        let key_space = self.get_key_space(scheme);

        let extension_index_specs = self.extension_index_specs.read().map_err(|_| {
            AppError::InternalServerError("Failed to acquire read lock on index_specs".to_string())
        })?;

        extension_index_specs.get(&key_space)
            .map(|index_specs| {
                 if let Some(index_specs) = index_specs.downcast_ref::<Arc<RwLock<DefaultIndexSpecs<T>>>>() {
                       return Ok(Arc::clone(index_specs));
                 }else{
                     Err(AppError::InternalServerError("Failed to remove index_specs".to_string()))
                 }
            })
            .unwrap_or_else(|| {
                Err(anyhow::anyhow!("No index specs found for extension_index type: {:?}, make sure you have called indexFor() before calling getIndexSpecs()", scheme.group_version_kind.clone()).into())
            })
    }

    pub(crate) fn contains(&self, scheme: &Scheme) -> AppResult<bool> {
        let key_space = self.get_key_space(scheme);
        let extension_index_specs = self.extension_index_specs.read().map_err(|_| {
            AppError::InternalServerError("Failed to acquire read lock on index_specs".to_string())
        })?;

        Ok(extension_index_specs.contains_key(&key_space))
    }

    pub(crate) fn remove_index_specs(&self, scheme: &Scheme) -> AppResult<()> {
        let key_space = self.get_key_space(scheme);
        let mut extension_index_specs = self.extension_index_specs.write().map_err(|_| {
            AppError::InternalServerError("Failed to acquire write lock on index_specs".to_string())
        })?;
        extension_index_specs.remove(&key_space);
        Ok(())
    }

    pub(crate) fn get_key_space(&self, _scheme: &Scheme) -> String {
        // scheme_manager::build_store_name_prefix(scheme)
        todo!();
    }
}

#[cfg(test)]
mod tests {
    use crate::extension::index::index::index_spec_registry::IndexSpecRegistry;
    use crate::extension::index::scheme_manager::Scheme;
    use crate::tests::model::fake_extension::FakeExtension;

    #[tokio::test]
    async fn index_for() {
        let index_spec_registry = IndexSpecRegistry::new();
        // let mut scheme_manager = get_scheme_manager!().unwrap();
        // scheme_manager.register::<FakeExtension>().await;
        let scheme = Scheme::new::<FakeExtension>().await;

        index_spec_registry
            .index_for::<FakeExtension>(&scheme)
            .unwrap();
        let extension_index_specs = index_spec_registry.extension_index_specs;
        let extension_index_specs = extension_index_specs.write().unwrap();
        assert_eq!(extension_index_specs.len(), 4);
    }

    #[tokio::test]
    async fn contains() {
        let index_spec_registry = IndexSpecRegistry::new();
        // let scheme_manager = get_scheme_manager!().unwrap();
        // scheme_manager.register::<FakeExtension>().await;
        let scheme = Scheme::new::<FakeExtension>().await;

        index_spec_registry
            .index_for::<FakeExtension>(&scheme)
            .unwrap();

        let contains = index_spec_registry.contains(&scheme).unwrap();

        assert!(contains);
    }
    #[tokio::test]
    async fn get_key_space() {
        let index_spec_registry = IndexSpecRegistry::new();

        // let scheme_manager = get_scheme_manager!().unwrap();
        // scheme_manager.register::<FakeExtension>().await;
        let scheme = Scheme::new::<FakeExtension>().await;

        let key_space = index_spec_registry.get_key_space(&scheme);

        assert_eq!(key_space, "/registry/test.halo.run/fakes");
    }

    #[tokio::test]
    async fn get_index_specs() {
        let index_spec_registry = IndexSpecRegistry::new();

        // let scheme_manager = get_scheme_manager!().unwrap();
        // scheme_manager.register::<FakeExtension>().await;
        let scheme = Scheme::new::<FakeExtension>().await;

        let index_specs = index_spec_registry.get_index_specs::<FakeExtension>(&scheme);

        assert!(index_specs.is_err());
        match index_specs {
            Ok(_) => {}
            Err(e) => assert!(
                e.to_string()
                    .contains("No index specs found for extension_index type: ")
            ),
        }

        let _ = index_spec_registry
            .index_for::<FakeExtension>(&scheme)
            .unwrap();

        let key_space = index_spec_registry.get_index_specs::<FakeExtension>(&scheme);

        assert!(key_space.is_ok());
    }
}
