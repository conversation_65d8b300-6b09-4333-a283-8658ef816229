use halo_model::ExtensionGVK;

use crate::config::app_error::{AppError, AppResult};
use crate::extension::index::index::query::index_descriptor::IndexDescriptor;
use crate::extension::index::index::query::index_entry::{IndexEntry, IndexEntryImpl};
use std::cell::RefCell;
use std::collections::HashMap;
use std::sync::Arc;
use std::sync::RwLock;

/// A container for IndexEntrys, it is used to store all IndexEntrys according to the IndexDescriptor.
/// This class is thread-safe.
pub struct IndexEntryContainer<T: ExtensionGVK> {
    index_entries: RwLock<HashMap<String, Arc<RefCell<IndexEntryImpl<T>>>>>,
}

impl<T: ExtensionGVK> IndexEntryContainer<T> {
    pub fn new() -> Self {
        IndexEntryContainer {
            index_entries: RwLock::new(HashMap::new()),
        }
    }

    /**
     * Add an `IndexEntry` to this container.
     *
     * @param entry the entry to add
     * @throws if the entry already exists
     */
    pub fn add(&self, index_entry: Arc<RefCell<IndexEntryImpl<T>>>) -> AppResult<()> {
        let index_descriptor = index_entry.borrow().get_index_descriptor().identity();

        let mut index_entries = self.index_entries.write().map_err(|_| {
            AppError::InternalServerError(
                "Failed to acquire write lock on index_entries".to_string(),
            )
        })?;

        if index_entries.contains_key(&index_descriptor) {
            return Err(
                anyhow::anyhow!("Index entry already exists for {:?}", index_descriptor).into(),
            );
        }
        index_entries.insert(index_descriptor, index_entry);
        Ok(())
    }

    /**
     * Get the `IndexEntry` for the given `IndexDescriptor`.
     *
     * @param index_descriptor the index descriptor
     * @return the index entry
     */
    pub fn get(
        &self,
        index_descriptor: &IndexDescriptor<T>,
    ) -> AppResult<Option<Arc<RefCell<IndexEntryImpl<T>>>>> {
        let index_entries = self.index_entries.read().map_err(|_| {
            AppError::InternalServerError(
                "Failed to acquire read lock on index_entries".to_string(),
            )
        })?;

        if let Some(index_entry) = index_entries.get(&index_descriptor.identity()) {
            Ok(Some(Arc::clone(index_entry)))
        } else {
            Ok(None)
        }
    }

    pub fn contains(&self, identity: &String) -> AppResult<bool> {
        let index_entries = self.index_entries.read().map_err(|_| {
            AppError::InternalServerError(
                "Failed to acquire read lock on index_entries".to_string(),
            )
        })?;

        Ok(index_entries.contains_key(identity))
    }

    pub fn remove(&self, identity: &String) -> AppResult<Option<Arc<RefCell<IndexEntryImpl<T>>>>> {
        let mut index_entries = self.index_entries.write().map_err(|_| {
            AppError::InternalServerError(
                "Failed to acquire write lock on index_entries".to_string(),
            )
        })?;

        if let Some(index_entry) = index_entries.remove(identity) {
            Ok(Some(Arc::clone(&index_entry)))
        } else {
            Ok(None)
        }
    }

    pub fn size(&self) -> AppResult<usize> {
        let index_entries = self.index_entries.read().map_err(|_| {
            AppError::InternalServerError(
                "Failed to acquire read lock on index_entries".to_string(),
            )
        })?;
        Ok(index_entries.len())
    }

    pub fn for_each<F>(&self, f: F) -> AppResult<()>
    where
        F: Fn(&Arc<RefCell<IndexEntryImpl<T>>>),
    {
        let index_entries = self.index_entries.read().map_err(|_| {
            AppError::InternalServerError(
                "Failed to acquire read lock on index_entries".to_string(),
            )
        })?;
        Ok(index_entries.iter().for_each(|(_, index_entry)| {
            f(index_entry);
        }))
    }

    pub fn iter(&self) -> AppResult<Vec<Arc<RefCell<IndexEntryImpl<T>>>>> {
        let index_entries = self.index_entries.read().map_err(|_| {
            AppError::InternalServerError(
                "Failed to acquire read lock on index_entries".to_string(),
            )
        })?;
        let mut result = Vec::new();
        for (_, index_entry) in index_entries.iter() {
            result.push(Arc::clone(index_entry));
        }
        Ok(result)
    }

    pub fn remove_fn<F>(&mut self, f: F) -> AppResult<Vec<Arc<RefCell<IndexEntryImpl<T>>>>>
    where
        F: Fn(&IndexDescriptor<T>) -> bool,
    {
        let mut index_entries = self.index_entries.write().map_err(|_| {
            AppError::InternalServerError(
                "Failed to acquire write lock on index_entries".to_string(),
            )
        })?;

        let to_remove = index_entries
            .iter()
            .filter_map(|(key, index_entry)| {
                if f(&index_entry.borrow().get_index_descriptor()) {
                    Some(key.to_owned())
                } else {
                    None
                }
            })
            .collect::<Vec<String>>();

        let mut vec = Vec::new();
        for key in to_remove {
            if let Some(index_entry) = index_entries.remove(&key) {
                vec.push(Arc::clone(&index_entry));
            }
        }
        Ok(vec)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::{
        extension::index::index::query::index_entry::IndexEntryImpl, tests::primary_key_index_spec,
    };

    #[test]
    fn add() {
        let container = IndexEntryContainer::new();

        let spec = primary_key_index_spec();
        let descriptor = IndexDescriptor::new(spec);
        let entry = IndexEntryImpl::new(descriptor);

        let entry = Arc::new(RefCell::new(entry));
        container.add(entry.clone()).unwrap();

        let spec = primary_key_index_spec();
        let descriptor = IndexDescriptor::new(spec);
        assert!(container.contains(&descriptor.identity()).unwrap());

        let result = container.add(entry.clone());
        assert!(result.is_err());
        assert_eq!(
            result.unwrap_err().to_string(),
            format!(
                "error:`Index entry already exists for {:?}`",
                descriptor.identity()
            )
        );
    }

    #[test]
    fn remove() {
        let container = IndexEntryContainer::new();

        let spec = primary_key_index_spec();
        let descriptor = IndexDescriptor::new(spec);
        let entry = IndexEntryImpl::new(descriptor);

        let entry = Arc::new(RefCell::new(entry));
        container.add(entry.clone()).unwrap();

        let spec = primary_key_index_spec();
        let descriptor = IndexDescriptor::new(spec);
        assert!(container.contains(&descriptor.identity()).unwrap());
        assert_eq!(container.size().unwrap(), 1);

        container.remove(&descriptor.identity()).unwrap();

        assert!(!container.contains(&descriptor.identity()).unwrap());
        assert_eq!(container.size().unwrap(), 0);
    }
}
