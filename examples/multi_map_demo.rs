use std::cmp::Ordering;

// 直接包含 MultiMap 的代码
#[path = "../src/util/map/multi_map.rs"]
mod multi_map;

#[path = "../src/util/map/comparator_map.rs"]
mod comparator_map;

use multi_map::{MultiMap, ComparatorMultiMap};

// 自定义比较器：按字符串长度排序
fn string_length_cmp(a: &String, b: &String) -> Ordering {
    a.len().cmp(&b.len()).then_with(|| a.cmp(b))
}

// 自定义比较器：逆序整数
fn reverse_int_cmp(a: &i32, b: &i32) -> Ordering {
    b.cmp(a)
}

fn main() {
    println!("=== MultiMap 优化演示 ===\n");

    // 1. 传统的 HashMap-based MultiMap
    println!("1. 传统 HashMap-based MultiMap:");
    let mut hash_map = MultiMap::new();
    
    hash_map.insert("fruits", "apple");
    hash_map.insert("fruits", "banana");
    hash_map.insert("fruits", "orange");
    hash_map.insert("colors", "red");
    hash_map.insert("colors", "blue");
    
    println!("HashMap MultiMap 内容:");
    for (key, values) in hash_map.iter_all() {
        println!("  {} => {:?}", key, values);
    }
    println!();

    // 2. 使用比较器的 ComparatorMultiMap (通过便利方法)
    println!("2. 使用比较器的 MultiMap (便利方法):");
    let mut comparator_map: ComparatorMultiMap<String, i32, _> = MultiMap::with_comparator(string_length_cmp);

    comparator_map.insert("a".to_string(), 1);
    comparator_map.insert("bb".to_string(), 2);
    comparator_map.insert("ccc".to_string(), 3);
    comparator_map.insert("a".to_string(), 10);  // 同一个键的多个值
    comparator_map.insert("bb".to_string(), 20);

    println!("ComparatorMultiMap 内容 (按字符串长度排序):");
    for (key, values) in comparator_map.iter_all() {
        println!("  '{}' (长度: {}) => {:?}", key, key.len(), values);
    }
    println!();

    // 3. 直接创建 ComparatorMultiMap
    println!("3. 直接创建 ComparatorMultiMap:");
    let mut direct_comparator_map = ComparatorMultiMap::new(reverse_int_cmp);
    
    direct_comparator_map.insert(5, "five");
    direct_comparator_map.insert(1, "one");
    direct_comparator_map.insert(3, "three");
    direct_comparator_map.insert(1, "uno");  // 同一个键的多个值
    direct_comparator_map.insert(5, "cinco");
    
    println!("ComparatorMultiMap 内容 (逆序排列):");
    for (key, values) in direct_comparator_map.iter_all() {
        println!("  {} => {:?}", key, values);
    }
    println!();

    // 4. 演示基本操作
    println!("4. 基本操作演示:");
    let mut demo_map: ComparatorMultiMap<String, i32, _> = MultiMap::with_comparator(string_length_cmp);
    
    // 插入多个值
    demo_map.insert("test".to_string(), 100);
    demo_map.insert("example".to_string(), 200);
    demo_map.insert("test".to_string(), 150);
    
    // 获取第一个值
    println!("获取 'test' 的第一个值: {:?}", demo_map.get(&"test".to_string()));
    
    // 获取所有值
    println!("获取 'test' 的所有值: {:?}", demo_map.get_vec(&"test".to_string()));
    
    // 检查是否包含键
    println!("是否包含 'test': {}", demo_map.contains_key(&"test".to_string()));
    println!("是否包含 'missing': {}", demo_map.contains_key(&"missing".to_string()));
    
    // 长度和是否为空
    println!("map 大小: {}", demo_map.len());
    println!("是否为空: {}", demo_map.is_empty());
    println!();

    // 5. 演示迭代器
    println!("5. 迭代器演示:");
    
    // 所有键
    println!("所有键:");
    for key in demo_map.keys() {
        println!("  '{}'", key);
    }
    
    // 所有值
    println!("所有值:");
    for value in demo_map.values() {
        println!("  {}", value);
    }
    
    // 所有键值对
    println!("所有键值对:");
    for (key, value) in demo_map.iter() {
        println!("  '{}' => {}", key, value);
    }
    println!();

    // 6. 演示批量插入
    println!("6. 批量插入演示:");
    let mut batch_map: ComparatorMultiMap<String, i32, _> = MultiMap::with_comparator(string_length_cmp);
    
    batch_map.insert_many("numbers".to_string(), vec![1, 2, 3, 4, 5]);
    batch_map.insert("x".to_string(), 10);
    batch_map.insert("numbers".to_string(), 6);  // 添加到现有向量
    
    println!("批量插入后的内容:");
    for (key, values) in batch_map.iter_all() {
        println!("  '{}' => {:?}", key, values);
    }
    println!();

    // 7. 演示移除操作
    println!("7. 移除操作演示:");
    let removed = demo_map.remove(&"test".to_string());
    println!("移除 'test': {:?}", removed);
    println!("移除后 map 大小: {}", demo_map.len());
    
    println!("移除后的内容:");
    for (key, values) in demo_map.iter_all() {
        println!("  '{}' => {:?}", key, values);
    }

    println!("\n=== 演示完成 ===");
    println!("✅ MultiMap 成功支持了 HashMap 和 ComparatorMap 两种模式！");
    println!("✅ 传入比较器就使用 ComparatorMap，不传就使用 HashMap！");
}
