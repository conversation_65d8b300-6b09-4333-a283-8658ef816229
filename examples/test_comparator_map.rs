use std::cmp::Ordering;
use std::collections::BTreeMap;
use std::fmt;
use std::rc::Rc;

pub struct ComparatorMap<K, V, F>
where
    F: Fn(&K, &K) -> Ordering,
{
    map: BTreeMap<KeyWrapper<K, F>, V>,
    cmp: Rc<F>,
}

struct KeyWrapper<K, F>(K, Rc<F>)
where
    F: Fn(&K, &K) -> Ordering;

impl<K, F> Clone for KeyWrapper<K, F>
where
    K: Clone,
    F: Fn(&K, &K) -> Ordering,
{
    fn clone(&self) -> Self {
        KeyWrapper(self.0.clone(), self.1.clone())
    }
}

impl<K, F> PartialEq for KeyWrapper<K, F>
where
    F: Fn(&K, &K) -> Ordering,
{
    fn eq(&self, other: &Self) -> bool {
        (self.1)(&self.0, &other.0) == Ordering::Equal
    }
}
impl<K, F> Eq for KeyWrapper<K, F> 
where 
    F: Fn(&K, &K) -> Ordering,
{}

impl<K, F> PartialOrd for KeyWrapper<K, F>
where
    F: Fn(&K, &K) -> Ordering,
{
    fn partial_cmp(&self, other: &Self) -> Option<Ordering> {
        Some((self.1)(&self.0, &other.0))
    }
}

impl<K, F> Ord for KeyWrapper<K, F>
where
    F: Fn(&K, &K) -> Ordering,
{
    fn cmp(&self, other: &Self) -> Ordering {
        (self.1)(&self.0, &other.0)
    }
}

impl<K, V, F> ComparatorMap<K, V, F>
where
    K: Clone,
    F: Fn(&K, &K) -> Ordering,
{
    // 创建一个新的 ComparatorMap
    pub fn new(cmp: F) -> Self {
        ComparatorMap {
            map: BTreeMap::new(),
            cmp: Rc::new(cmp),
        }
    }

    // 插入键值对，返回旧值（如果存在）
    pub fn insert(&mut self, key: K, value: V) -> Option<V> {
        let wrapper = KeyWrapper(key, self.cmp.clone());
        self.map.insert(wrapper, value)
    }

    // 获取指定键的值的引用
    pub fn get(&self, key: &K) -> Option<&V> {
        // 直接遍历查找匹配的键
        self.map.iter()
            .find(|(k, _)| (self.cmp)(&k.0, key) == Ordering::Equal)
            .map(|(_, v)| v)
    }

    // 检查是否包含指定键
    pub fn contains_key(&self, key: &K) -> bool {
        self.map.keys()
            .any(|k| (self.cmp)(&k.0, key) == Ordering::Equal)
    }

    // 返回 map 中键值对的数量
    pub fn len(&self) -> usize {
        self.map.len()
    }

    // 检查 map 是否为空
    pub fn is_empty(&self) -> bool {
        self.map.is_empty()
    }

    // 获取所有键的迭代器
    pub fn keys(&self) -> impl Iterator<Item = &K> {
        self.map.keys().map(|wrapper| &wrapper.0)
    }

    // 获取所有值的迭代器
    pub fn values(&self) -> impl Iterator<Item = &V> {
        self.map.values()
    }

    // 获取所有键值对的迭代器
    pub fn iter(&self) -> impl Iterator<Item = (&K, &V)> {
        self.map.iter().map(|(wrapper, value)| (&wrapper.0, value))
    }

    // 获取第一个键值对的引用（按比较器排序）
    pub fn first_key_value(&self) -> Option<(&K, &V)> {
        self.map.first_key_value()
            .map(|(wrapper, value)| (&wrapper.0, value))
    }

    // 获取最后一个键值对的引用（按比较器排序）
    pub fn last_key_value(&self) -> Option<(&K, &V)> {
        self.map.last_key_value()
            .map(|(wrapper, value)| (&wrapper.0, value))
    }
}

// 自定义比较器：按字符串长度排序
fn string_length_cmp(a: &String, b: &String) -> Ordering {
    a.len().cmp(&b.len()).then_with(|| a.cmp(b))
}

// 自定义比较器：逆序整数
fn reverse_int_cmp(a: &i32, b: &i32) -> Ordering {
    b.cmp(a)
}

fn main() {
    println!("=== ComparatorMap 测试 ===\n");

    // 演示字符串长度比较器
    println!("1. 字符串长度比较器演示:");
    let mut string_map = ComparatorMap::new(string_length_cmp);
    
    string_map.insert("hello".to_string(), 1);
    string_map.insert("hi".to_string(), 2);
    string_map.insert("world".to_string(), 3);
    string_map.insert("a".to_string(), 4);
    string_map.insert("programming".to_string(), 5);
    
    println!("插入的键值对（按长度排序）:");
    for (key, value) in string_map.iter() {
        println!("  '{}' (长度: {}) => {}", key, key.len(), value);
    }
    
    println!("第一个键值对: {:?}", string_map.first_key_value());
    println!("最后一个键值对: {:?}", string_map.last_key_value());
    println!();

    // 演示逆序整数比较器
    println!("2. 逆序整数比较器演示:");
    let mut int_map = ComparatorMap::new(reverse_int_cmp);
    
    int_map.insert(1, "one");
    int_map.insert(5, "five");
    int_map.insert(3, "three");
    int_map.insert(2, "two");
    int_map.insert(4, "four");
    
    println!("插入的键值对（逆序排列）:");
    for (key, value) in int_map.iter() {
        println!("  {} => {}", key, value);
    }
    
    println!("第一个键值对: {:?}", int_map.first_key_value());
    println!("最后一个键值对: {:?}", int_map.last_key_value());
    println!();

    // 演示基本操作
    println!("3. 基本操作演示:");
    let mut demo_map = ComparatorMap::new(string_length_cmp);
    
    // 插入
    demo_map.insert("test".to_string(), 100);
    demo_map.insert("example".to_string(), 200);
    
    // 查找
    println!("查找 'test': {:?}", demo_map.get(&"test".to_string()));
    println!("查找 'missing': {:?}", demo_map.get(&"missing".to_string()));
    
    // 更新
    demo_map.insert("test".to_string(), 150);
    println!("更新后查找 'test': {:?}", demo_map.get(&"test".to_string()));
    
    println!("map 大小: {}", demo_map.len());
    println!("是否为空: {}", demo_map.is_empty());
    println!();

    // 演示键迭代器
    println!("4. 键迭代器演示:");
    let keys: Vec<_> = demo_map.keys().cloned().collect();
    println!("所有键: {:?}", keys);
    
    let values: Vec<_> = demo_map.values().cloned().collect();
    println!("所有值: {:?}", values);

    println!("\n=== 测试完成 ===");
    println!("✅ ComparatorMap 成功实现了自定义比较器功能！");
    println!("✅ 比较器不需要 Clone trait，只需要引用即可！");
}
